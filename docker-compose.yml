services:

  # Semantic Router External Processor Service
  semantic-router:
    build:
      context: .
      dockerfile: Dockerfile.extproc
    container_name: semantic-router
    ports:
      - "50051:50051"
    volumes:
      - ./config:/app/config:ro
      - ./models:/app/models:ro
    environment:
      - LD_LIBRARY_PATH=/app/lib
      - CONFIG_FILE=${CONFIG_FILE:-/app/config/config.yaml}
    networks:
      - semantic-network
    healthcheck:
      test: ["CMD", "curl", "-f", "localhost:8080/health"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s

  # Envoy Proxy Service
  envoy:
    image: envoyproxy/envoy:v1.31.7
    container_name: envoy-proxy
    ports:
      - "8801:8801"  # Main proxy port
      - "19000:19000"  # Admin interface
    volumes:
      - ./config/envoy-docker.yaml:/etc/envoy/envoy.yaml:ro
    command: ["/usr/local/bin/envoy", "-c", "/etc/envoy/envoy.yaml", "--component-log-level", "ext_proc:trace,router:trace,http:trace"]
    depends_on:
      semantic-router:
        condition: service_healthy
    networks:
      - semantic-network
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:19000/ready"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 10s

  # Mock vLLM service for testing profile
  mock-vllm:
    build:
      context: ./tools/mock-vllm
      dockerfile: Dockerfile
    container_name: mock-vllm
    profiles: ["testing"]
    ports:
      - "8000:8000"
    networks:
      semantic-network:
        ipv4_address: ***********
    healthcheck:
      test: ["CMD", "curl", "-fsS", "http://localhost:8000/health"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 5s

  # Prometheus and Grafana for observability
  prometheus:
    image: prom/prometheus:v2.53.0
    container_name: prometheus
    volumes:
      - ./config/prometheus.yaml:/etc/prometheus/prometheus.yaml:ro
    command:
      - --config.file=/etc/prometheus/prometheus.yaml
      - --storage.tsdb.retention.time=15d
    ports:
      - "9090:9090"
    networks:
      - semantic-network

  grafana:
    image: grafana/grafana:11.5.1
    container_name: grafana
    environment:
      - GF_SECURITY_ADMIN_USER=admin
      - GF_SECURITY_ADMIN_PASSWORD=admin
    ports:
      - "3000:3000"
    volumes:
      - ./config/grafana/datasource.yaml:/etc/grafana/provisioning/datasources/datasource.yaml:ro
      - ./config/grafana/dashboards.yaml:/etc/grafana/provisioning/dashboards/dashboards.yaml:ro
      - ./deploy/llm-router-dashboard.json:/etc/grafana/provisioning/dashboards/llm-router-dashboard.json:ro
    networks:
      - semantic-network

networks:
  semantic-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

volumes:
  models-cache:
    driver: local
