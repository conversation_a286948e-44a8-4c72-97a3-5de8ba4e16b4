bert_model:
  model_id: sentence-transformers/all-MiniLM-L12-v2
  threshold: 0.6
  use_cpu: true
semantic_cache:
  enabled: true
  similarity_threshold: 0.8
  max_entries: 1000
  ttl_seconds: 3600
tools:
  enabled: true  # Set to true to enable automatic tool selection
  top_k: 3        # Number of most relevant tools to select
  similarity_threshold: 0.2  # Threshold for tool similarity
  tools_db_path: "config/tools_db.json"
  fallback_to_empty: true  # If true, return no tools on failure; if false, return error
prompt_guard:
  enabled: true
  use_modernbert: true
  model_id: "models/jailbreak_classifier_modernbert-base_model"
  threshold: 0.7
  use_cpu: true
  jailbreak_mapping_path: "models/jailbreak_classifier_modernbert-base_model/jailbreak_type_mapping.json"

# vLLM Endpoints Configuration - supports multiple endpoints, each can serve multiple models
# IMPORTANT: 'address' field must be a valid IP address (IPv4 or IPv6)
# Supported formats: 127.0.0.1, ***********, ::1, 2001:db8::1
# NOT supported: domain names (example.com), protocol prefixes (http://), paths (/api), ports in address (use 'port' field)
vllm_endpoints:
  - name: "endpoint1"
    address: "127.0.0.1"  # IPv4 address - REQUIRED format
    port: 11434
    models:
      - "phi4"
      - "gemma3:27b"
    weight: 1  # Load balancing weight
  - name: "endpoint2"
    address: "127.0.0.1"  # IPv4 address - REQUIRED format
    port: 11434
    models:
      - "mistral-small3.1"
    weight: 1
  - name: "endpoint3"
    address: "127.0.0.1"  # IPv4 address - REQUIRED format
    port: 11434
    models:
      - "phi4"  # Same model can be served by multiple endpoints for redundancy
      - "mistral-small3.1"
    weight: 2  # Higher weight for more powerful endpoint

model_config:
  phi4:
    pii_policy:
      allow_by_default: false  # Deny all PII by default
      pii_types_allowed: ["EMAIL_ADDRESS", "PERSON", "GPE", "PHONE_NUMBER"]  # Only allow these specific PII types
    # Specify which endpoints can serve this model (optional - if not specified, uses all endpoints that list this model)
    preferred_endpoints: ["endpoint1", "endpoint3"]
  gemma3:27b:
    pii_policy:
      allow_by_default: false  # Deny all PII by default
      pii_types_allowed: ["EMAIL_ADDRESS", "PERSON", "GPE", "PHONE_NUMBER"]  # Only allow these specific PII types
    preferred_endpoints: ["endpoint1"]
  "mistral-small3.1":
    pii_policy:
      allow_by_default: false  # Deny all PII by default
      pii_types_allowed: ["EMAIL_ADDRESS", "PERSON", "GPE", "PHONE_NUMBER"]  # Only allow these specific PII types
    preferred_endpoints: ["endpoint2", "endpoint3"]

# Classifier configuration for text classification
classifier:
  category_model:
    model_id: "models/category_classifier_modernbert-base_model"  # TODO: Use local model for now before the code can download the entire model from huggingface
    use_modernbert: true
    threshold: 0.6
    use_cpu: true
    category_mapping_path: "models/category_classifier_modernbert-base_model/category_mapping.json"
  pii_model:
    model_id: "models/pii_classifier_modernbert-base_presidio_token_model"
    use_modernbert: true
    threshold: 0.7
    use_cpu: true
    pii_mapping_path: "models/pii_classifier_modernbert-base_presidio_token_model/pii_type_mapping.json"
categories:
  - name: business
    model_scores:
      - model: phi4
        score: 0.8
      - model: gemma3:27b
        score: 0.4
      - model: mistral-small3.1
        score: 0.2
  - name: law
    model_scores:
      - model: gemma3:27b
        score: 0.8
      - model: phi4
        score: 0.6
      - model: mistral-small3.1
        score: 0.4
  - name: psychology
    model_scores:
      - model: mistral-small3.1
        score: 0.6
      - model: gemma3:27b
        score: 0.4
      - model: phi4
        score: 0.4
  - name: biology
    model_scores:
      - model: mistral-small3.1
        score: 0.8
      - model: gemma3:27b
        score: 0.6
      - model: phi4
        score: 0.2
  - name: chemistry
    model_scores:
      - model: mistral-small3.1
        score: 0.8
      - model: gemma3:27b
        score: 0.6
      - model: phi4
        score: 0.6
  - name: history
    model_scores:
      - model: mistral-small3.1
        score: 0.8
      - model: phi4
        score: 0.6
      - model: gemma3:27b
        score: 0.4
  - name: other
    model_scores:
      - model: gemma3:27b
        score: 0.8
      - model: phi4
        score: 0.6
      - model: mistral-small3.1
        score: 0.6
  - name: health
    model_scores:
      - model: gemma3:27b
        score: 0.8
      - model: phi4
        score: 0.8
      - model: mistral-small3.1
        score: 0.6
  - name: economics
    model_scores:
      - model: gemma3:27b
        score: 0.8
      - model: mistral-small3.1
        score: 0.8
      - model: phi4
        score: 0.0
  - name: math
    model_scores:
      - model: phi4
        score: 1.0
      - model: mistral-small3.1
        score: 0.8
      - model: gemma3:27b
        score: 0.6
  - name: physics
    model_scores:
      - model: gemma3:27b
        score: 0.4
      - model: phi4
        score: 0.4
      - model: mistral-small3.1
        score: 0.4
  - name: computer science
    model_scores:
      - model: gemma3:27b
        score: 0.6
      - model: mistral-small3.1
        score: 0.6
      - model: phi4
        score: 0.0
  - name: philosophy
    model_scores:
      - model: phi4
        score: 0.6
      - model: gemma3:27b
        score: 0.2
      - model: mistral-small3.1
        score: 0.2
  - name: engineering
    model_scores:
      - model: gemma3:27b
        score: 0.6
      - model: mistral-small3.1
        score: 0.6
      - model: phi4
        score: 0.2

default_model: mistral-small3.1
