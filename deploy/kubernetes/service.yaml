apiVersion: v1
kind: Service
metadata:
  name: semantic-router
  labels:
    app: semantic-router
spec:
  type: ClusterIP
  ports:
  - port: 50051
    targetPort: grpc
    protocol: TCP
    name: grpc
  selector:
    app: semantic-router
---
apiVersion: v1
kind: Service
metadata:
  name: semantic-router-metrics
  labels:
    app: semantic-router
    service: metrics
spec:
  type: ClusterIP
  ports:
  - port: 9190
    targetPort: metrics
    protocol: TCP
    name: metrics
  selector:
    app: semantic-router
