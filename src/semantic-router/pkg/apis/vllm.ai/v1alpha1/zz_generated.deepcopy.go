//go:build !ignore_autogenerated

/*
Copyright 2025 vLLM Semantic Router.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by controller-gen. DO NOT EDIT.

package v1alpha1

import (
	"k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
)

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *Filter) DeepCopyInto(out *Filter) {
	*out = *in
	if in.Config != nil {
		in, out := &in.Config, &out.Config
		*out = new(runtime.RawExtension)
		(*in).DeepCopyInto(*out)
	}
	if in.Enabled != nil {
		in, out := &in.Enabled, &out.Enabled
		*out = new(bool)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new Filter.
func (in *Filter) DeepCopy() *Filter {
	if in == nil {
		return nil
	}
	out := new(Filter)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *FilterCondition) DeepCopyInto(out *FilterCondition) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new FilterCondition.
func (in *FilterCondition) DeepCopy() *FilterCondition {
	if in == nil {
		return nil
	}
	out := new(FilterCondition)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *FilterConfigHelper) DeepCopyInto(out *FilterConfigHelper) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new FilterConfigHelper.
func (in *FilterConfigHelper) DeepCopy() *FilterConfigHelper {
	if in == nil {
		return nil
	}
	out := new(FilterConfigHelper)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *Intent) DeepCopyInto(out *Intent) {
	*out = *in
	if in.Threshold != nil {
		in, out := &in.Threshold, &out.Threshold
		*out = new(float64)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new Intent.
func (in *Intent) DeepCopy() *Intent {
	if in == nil {
		return nil
	}
	out := new(Intent)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ModelRef) DeepCopyInto(out *ModelRef) {
	*out = *in
	if in.Weight != nil {
		in, out := &in.Weight, &out.Weight
		*out = new(int32)
		**out = **in
	}
	if in.Priority != nil {
		in, out := &in.Priority, &out.Priority
		*out = new(int32)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ModelRef.
func (in *ModelRef) DeepCopy() *ModelRef {
	if in == nil {
		return nil
	}
	out := new(ModelRef)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *PIIDetectionConfig) DeepCopyInto(out *PIIDetectionConfig) {
	*out = *in
	if in.AllowByDefault != nil {
		in, out := &in.AllowByDefault, &out.AllowByDefault
		*out = new(bool)
		**out = **in
	}
	if in.PIITypesAllowed != nil {
		in, out := &in.PIITypesAllowed, &out.PIITypesAllowed
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	if in.Threshold != nil {
		in, out := &in.Threshold, &out.Threshold
		*out = new(float64)
		**out = **in
	}
	if in.Action != nil {
		in, out := &in.Action, &out.Action
		*out = new(string)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new PIIDetectionConfig.
func (in *PIIDetectionConfig) DeepCopy() *PIIDetectionConfig {
	if in == nil {
		return nil
	}
	out := new(PIIDetectionConfig)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *PromptGuardConfig) DeepCopyInto(out *PromptGuardConfig) {
	*out = *in
	if in.Threshold != nil {
		in, out := &in.Threshold, &out.Threshold
		*out = new(float64)
		**out = **in
	}
	if in.Action != nil {
		in, out := &in.Action, &out.Action
		*out = new(string)
		**out = **in
	}
	if in.CustomRules != nil {
		in, out := &in.CustomRules, &out.CustomRules
		*out = make([]SecurityRule, len(*in))
		copy(*out, *in)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new PromptGuardConfig.
func (in *PromptGuardConfig) DeepCopy() *PromptGuardConfig {
	if in == nil {
		return nil
	}
	out := new(PromptGuardConfig)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ReasoningControlConfig) DeepCopyInto(out *ReasoningControlConfig) {
	*out = *in
	if in.ReasonFamily != nil {
		in, out := &in.ReasonFamily, &out.ReasonFamily
		*out = new(string)
		**out = **in
	}
	if in.EnableReasoning != nil {
		in, out := &in.EnableReasoning, &out.EnableReasoning
		*out = new(bool)
		**out = **in
	}
	if in.ReasoningEffort != nil {
		in, out := &in.ReasoningEffort, &out.ReasoningEffort
		*out = new(string)
		**out = **in
	}
	if in.MaxReasoningSteps != nil {
		in, out := &in.MaxReasoningSteps, &out.MaxReasoningSteps
		*out = new(int32)
		**out = **in
	}
	if in.ReasoningTimeout != nil {
		in, out := &in.ReasoningTimeout, &out.ReasoningTimeout
		*out = new(int32)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ReasoningControlConfig.
func (in *ReasoningControlConfig) DeepCopy() *ReasoningControlConfig {
	if in == nil {
		return nil
	}
	out := new(ReasoningControlConfig)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *RouteRule) DeepCopyInto(out *RouteRule) {
	*out = *in
	if in.Intents != nil {
		in, out := &in.Intents, &out.Intents
		*out = make([]Intent, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	if in.ModelRefs != nil {
		in, out := &in.ModelRefs, &out.ModelRefs
		*out = make([]ModelRef, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	if in.Filters != nil {
		in, out := &in.Filters, &out.Filters
		*out = make([]Filter, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	if in.DefaultModel != nil {
		in, out := &in.DefaultModel, &out.DefaultModel
		*out = new(ModelRef)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new RouteRule.
func (in *RouteRule) DeepCopy() *RouteRule {
	if in == nil {
		return nil
	}
	out := new(RouteRule)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *SecurityRule) DeepCopyInto(out *SecurityRule) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new SecurityRule.
func (in *SecurityRule) DeepCopy() *SecurityRule {
	if in == nil {
		return nil
	}
	out := new(SecurityRule)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *SemanticCacheConfig) DeepCopyInto(out *SemanticCacheConfig) {
	*out = *in
	if in.SimilarityThreshold != nil {
		in, out := &in.SimilarityThreshold, &out.SimilarityThreshold
		*out = new(float64)
		**out = **in
	}
	if in.MaxEntries != nil {
		in, out := &in.MaxEntries, &out.MaxEntries
		*out = new(int32)
		**out = **in
	}
	if in.TTLSeconds != nil {
		in, out := &in.TTLSeconds, &out.TTLSeconds
		*out = new(int32)
		**out = **in
	}
	if in.Backend != nil {
		in, out := &in.Backend, &out.Backend
		*out = new(string)
		**out = **in
	}
	if in.BackendConfig != nil {
		in, out := &in.BackendConfig, &out.BackendConfig
		*out = make(map[string]string, len(*in))
		for key, val := range *in {
			(*out)[key] = val
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new SemanticCacheConfig.
func (in *SemanticCacheConfig) DeepCopy() *SemanticCacheConfig {
	if in == nil {
		return nil
	}
	out := new(SemanticCacheConfig)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *SemanticRoute) DeepCopyInto(out *SemanticRoute) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ObjectMeta.DeepCopyInto(&out.ObjectMeta)
	in.Spec.DeepCopyInto(&out.Spec)
	in.Status.DeepCopyInto(&out.Status)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new SemanticRoute.
func (in *SemanticRoute) DeepCopy() *SemanticRoute {
	if in == nil {
		return nil
	}
	out := new(SemanticRoute)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *SemanticRoute) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *SemanticRouteList) DeepCopyInto(out *SemanticRouteList) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ListMeta.DeepCopyInto(&out.ListMeta)
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]SemanticRoute, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new SemanticRouteList.
func (in *SemanticRouteList) DeepCopy() *SemanticRouteList {
	if in == nil {
		return nil
	}
	out := new(SemanticRouteList)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *SemanticRouteList) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *SemanticRouteSpec) DeepCopyInto(out *SemanticRouteSpec) {
	*out = *in
	if in.Rules != nil {
		in, out := &in.Rules, &out.Rules
		*out = make([]RouteRule, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new SemanticRouteSpec.
func (in *SemanticRouteSpec) DeepCopy() *SemanticRouteSpec {
	if in == nil {
		return nil
	}
	out := new(SemanticRouteSpec)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *SemanticRouteStatus) DeepCopyInto(out *SemanticRouteStatus) {
	*out = *in
	if in.Conditions != nil {
		in, out := &in.Conditions, &out.Conditions
		*out = make([]v1.Condition, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new SemanticRouteStatus.
func (in *SemanticRouteStatus) DeepCopy() *SemanticRouteStatus {
	if in == nil {
		return nil
	}
	out := new(SemanticRouteStatus)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ToolSelectionConfig) DeepCopyInto(out *ToolSelectionConfig) {
	*out = *in
	if in.TopK != nil {
		in, out := &in.TopK, &out.TopK
		*out = new(int32)
		**out = **in
	}
	if in.SimilarityThreshold != nil {
		in, out := &in.SimilarityThreshold, &out.SimilarityThreshold
		*out = new(float64)
		**out = **in
	}
	if in.ToolsDBPath != nil {
		in, out := &in.ToolsDBPath, &out.ToolsDBPath
		*out = new(string)
		**out = **in
	}
	if in.FallbackToEmpty != nil {
		in, out := &in.FallbackToEmpty, &out.FallbackToEmpty
		*out = new(bool)
		**out = **in
	}
	if in.Categories != nil {
		in, out := &in.Categories, &out.Categories
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	if in.Tags != nil {
		in, out := &in.Tags, &out.Tags
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ToolSelectionConfig.
func (in *ToolSelectionConfig) DeepCopy() *ToolSelectionConfig {
	if in == nil {
		return nil
	}
	out := new(ToolSelectionConfig)
	in.DeepCopyInto(out)
	return out
}
