[build-system]
requires = ["setuptools>=45", "wheel", "setuptools_scm[toml]>=6.2"]
build-backend = "setuptools.build_meta"

[project]
name = "vllm-semantic-router-bench"
version = "1.0.0"
description = "Comprehensive benchmark suite for semantic router vs direct vLLM evaluation across multiple reasoning datasets"
readme = "README.md"
requires-python = ">=3.8"
license = {text = "Apache-2.0"}
authors = [
    {name = "vLLM Semantic Router Team"},
]
keywords = [
    "vllm-semantic-router",
    "benchmark", 
    "vllm",
    "llm",
    "evaluation",
    "reasoning",
    "multiple-choice",
    "mmlu",
    "arc",
    "gpqa",
    "commonsense",
    "hellaswag",
    "truthfulqa",
]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "Intended Audience :: Science/Research", 
    "License :: OSI Approved :: Apache Software License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
    "Topic :: Software Development :: Testing",
    "Topic :: System :: Benchmark",
]
dependencies = [
    "openai>=1.0.0",
    "datasets>=2.14.0",
    "pandas>=1.5.0",
    "numpy>=1.21.0",
    "tqdm>=4.64.0",
    "requests>=2.28.0",
    "matplotlib>=3.5.0",
    "seaborn>=0.11.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=6.0",
    "black>=22.0",
    "flake8>=4.0", 
    "mypy>=0.950",
    "pre-commit>=2.15.0",
]
plotting = [
    "matplotlib>=3.5.0",
    "seaborn>=0.11.0",
]

[project.urls]
Homepage = "https://github.com/vllm-project/semantic-router"
Documentation = "https://vllm-semantic-router.com"
Repository = "https://github.com/vllm-project/semantic-router"
"Bug Tracker" = "https://github.com/vllm-project/semantic-router/issues"

[project.scripts]
vllm-semantic-router-bench = "vllm_semantic_router_bench.cli:main"
router-bench = "vllm_semantic_router_bench.router_reason_bench_multi_dataset:main"
bench-plot = "vllm_semantic_router_bench.bench_plot:main"

[tool.setuptools.packages.find]
where = ["."]
include = ["vllm_semantic_router_bench*"]

[tool.setuptools.package-data]
vllm_semantic_router_bench = ["*.md", "dataset_implementations/*.py"]

[tool.black]
line-length = 88
target-version = ['py38', 'py39', 'py310', 'py311', 'py312']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["vllm_semantic_router_bench"]

[tool.mypy]
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = "-v --tb=short"
