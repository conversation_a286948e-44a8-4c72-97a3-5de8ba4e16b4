# Build the Rust library using Makefile
FROM rust:1.85 as rust-builder

# Install make and other build dependencies
RUN apt-get update && apt-get install -y \
    make \
    build-essential \
    pkg-config \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /app

# Copy only essential files for Rust build
COPY tools/make/ tools/make/
COPY Makefile ./
COPY candle-binding/Cargo.toml candle-binding/
COPY candle-binding/src/ candle-binding/src/

# Use Makefile to build the Rust library
RUN make rust

# Build the Go application
FROM golang:1.24 as go-builder

WORKDIR /app

# Copy Go module files first for better layer caching
RUN mkdir -p src/semantic-router
COPY src/semantic-router/go.mod src/semantic-router/go.sum src/semantic-router/
COPY candle-binding/go.mod candle-binding/semantic-router.go candle-binding/

# Copy semantic-router source code
COPY src/semantic-router/ src/semantic-router/

# Copy the built Rust library from rust-builder
COPY --from=rust-builder /app/candle-binding/target/release/libcandle_semantic_router.so /app/candle-binding/target/release/

# Set environment variables for CGO to find the library
ENV CGO_ENABLED=1
ENV LD_LIBRARY_PATH=/app/candle-binding/target/release

# Build the router binary
RUN mkdir -p bin && cd src/semantic-router && go build -o ../../bin/router cmd/main.go

# Final stage: copy the binary and the shared library
FROM quay.io/centos/centos:stream9

WORKDIR /app

COPY --from=go-builder /app/bin/router /app/extproc-server
COPY --from=go-builder /app/candle-binding/target/release/libcandle_semantic_router.so /app/lib/
COPY config/config.yaml /app/config/

ENV LD_LIBRARY_PATH=/app/lib

EXPOSE 50051

# Copy entrypoint to allow switching config via env var CONFIG_FILE
COPY scripts/entrypoint.sh /app/entrypoint.sh
RUN chmod +x /app/entrypoint.sh

ENTRYPOINT ["/app/entrypoint.sh"]
