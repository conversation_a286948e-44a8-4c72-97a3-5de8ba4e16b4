name: Create and publish Docker image for extproc

on:
  workflow_dispatch:
  workflow_call:
    inputs:
      tag_suffix:
        description: 'Custom tag suffix for the Docker image'
        required: false
        type: string
        default: ''
      is_nightly:
        description: 'Whether this is a nightly build'
        required: false
        type: boolean
        default: false
  push:
    branches: [ "main" ]

jobs:
  build_and_push:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write

    steps:
    - name: Check out the repo
      uses: actions/checkout@v4

    - name: Log in to GitHub Container Registry
      uses: docker/login-action@v3
      with:
        registry: ghcr.io
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}

    - name: Generate date tag for nightly builds
      id: date
      if: inputs.is_nightly == true
      run: echo "date_tag=$(date +'%Y%m%d')" >> $GITHUB_OUTPUT

    - name: Set lowercase repository owner
      run: echo "REPOSITORY_OWNER_LOWER=$(echo $GITHUB_REPOSITORY_OWNER | tr '[:upper:]' '[:lower:]')" >> $GITHUB_ENV

    - name: Build and push Docker image
      uses: docker/build-push-action@v5
      with:
        context: .
        file: ./Dockerfile.extproc
        push: ${{ github.event_name != 'pull_request' }} # Only push on merge to main, not on PRs
        tags: |
          ${{ inputs.is_nightly == true && format('ghcr.io/{0}/semantic-router/extproc:nightly-{1}', env.REPOSITORY_OWNER_LOWER, steps.date.outputs.date_tag) || format('ghcr.io/{0}/semantic-router/extproc:{1}', env.REPOSITORY_OWNER_LOWER, github.sha) }}
          ${{ inputs.is_nightly != true && format('ghcr.io/{0}/semantic-router/extproc:latest', env.REPOSITORY_OWNER_LOWER) || '' }}
