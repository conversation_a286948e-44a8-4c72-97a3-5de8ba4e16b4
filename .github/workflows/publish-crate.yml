name: Publish Rust Crate

on:
  push:
    tags:
      - 'v*'  # Triggers on version tags like v1.0.0, v2.1.3, etc.
  workflow_dispatch:  # Allow manual triggering

jobs:
  publish:
    runs-on: ubuntu-latest
    
    steps:
    - name: Check out the repo
      uses: actions/checkout@v4

    - name: Extract tag name and validate
      id: extract_tag
      run: |
        echo "GITHUB_REF: $GITHUB_REF"
        if [[ "$GITHUB_REF" == refs/tags/v* ]]; then
          TAG_NAME=${GITHUB_REF#refs/tags/}
          TAG_VERSION=${GITHUB_REF#refs/tags/v}
          echo "tag=$TAG_NAME" >> $GITHUB_OUTPUT
          echo "version=$TAG_VERSION" >> $GITHUB_OUTPUT
          echo "Valid version tag detected: $TAG_NAME (version: $TAG_VERSION)"
        elif [[ "$GITHUB_EVENT_NAME" == "workflow_dispatch" ]]; then
          echo "Manual workflow dispatch - skipping version check"
          echo "tag=manual" >> $GITHUB_OUTPUT
          echo "version=manual" >> $GITHUB_OUTPUT
        else
          echo "::error::This workflow should only run on version tags (v*.*.*) or manual dispatch"
          echo "::error::Current ref: $GITHUB_REF"
          exit 1
        fi

    - name: Set up Rust
      uses: dtolnay/rust-toolchain@stable
      with:
        toolchain: 1.85

    - name: Install system dependencies
      run: |
        sudo apt-get update
        sudo apt-get install -y \
          make \
          build-essential \
          pkg-config

    - name: Cache Rust dependencies
      uses: actions/cache@v4
      with:
        path: |
          ~/.cargo/bin/
          ~/.cargo/registry/index/
          ~/.cargo/registry/cache/
          ~/.cargo/git/db/
          candle-binding/target/
        key: ${{ runner.os }}-cargo-publish-${{ hashFiles('**/Cargo.lock', '**/Cargo.toml') }}

    - name: Verify crate version matches tag
      if: steps.extract_tag.outputs.version != 'manual'
      working-directory: candle-binding
      run: |
        CRATE_VERSION=$(cargo metadata --no-deps --format-version 1 | jq -r '.packages[0].version')
        TAG_VERSION="${{ steps.extract_tag.outputs.version }}"
        echo "Crate version: $CRATE_VERSION"
        echo "Tag version: $TAG_VERSION" 
        if [ "$CRATE_VERSION" != "$TAG_VERSION" ]; then
          echo "::error::Crate version ($CRATE_VERSION) does not match tag version ($TAG_VERSION)"
          exit 1
        fi

    - name: Run tests
      working-directory: candle-binding
      run: cargo test --verbose

    - name: Check crate
      working-directory: candle-binding
      run: cargo check --verbose

    - name: Build crate
      working-directory: candle-binding
      run: cargo build --release --verbose

    - name: Dry run publish
      working-directory: candle-binding
      run: cargo publish --dry-run --verbose

    - name: Publish to crates.io
      working-directory: candle-binding
      run: cargo publish --verbose
      env:
        CARGO_REGISTRY_TOKEN: ${{ secrets.CRATES_TOKEN }}

    - name: Create GitHub Release
      if: steps.extract_tag.outputs.version != 'manual'
      uses: softprops/action-gh-release@v1
      with:
        tag_name: ${{ steps.extract_tag.outputs.tag }}
        name: Release ${{ steps.extract_tag.outputs.tag }}
        draft: false
        prerelease: false
        generate_release_notes: true
        files: |
          candle-binding/target/release/libcandle_semantic_router.a
          candle-binding/target/release/libcandle_semantic_router.so
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
