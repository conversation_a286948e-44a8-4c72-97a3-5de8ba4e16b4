name: Create and publish Docker release image

on:
  push:
    tags:
      - 'v*'  # Triggers on version tags like v1.0.0, v2.1.3, etc.

jobs:
  build_and_push:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write

    steps:
    - name: Check out the repo
      uses: actions/checkout@v4

    - name: Extract tag name
      id: extract_tag
      run: echo "tag=${GITHUB_REF#refs/tags/}" >> $GITHUB_OUTPUT

    - name: Set lowercase repository owner
      run: echo "REPOSITORY_OWNER_LOWER=$(echo $GITHUB_REPOSITORY_OWNER | tr '[:upper:]' '[:lower:]')" >> $GITHUB_ENV

    - name: Log in to GitHub Container Registry
      uses: docker/login-action@v3
      with:
        registry: ghcr.io
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}

    - name: Build and push Docker image
      uses: docker/build-push-action@v5
      with:
        context: .
        file: ./Dockerfile.extproc
        push: true
        tags: |
          ghcr.io/${{ env.REPOSITORY_OWNER_LOWER }}/semantic-router/extproc:${{ steps.extract_tag.outputs.tag }}
          ghcr.io/${{ env.REPOSITORY_OWNER_LOWER }}/semantic-router/extproc:latest
