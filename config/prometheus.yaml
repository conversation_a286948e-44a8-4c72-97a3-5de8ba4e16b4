global:
  scrape_interval: 10s
  evaluation_interval: 10s

scrape_configs:
  # Semantic Router
  - job_name: semantic-router
    metrics_path: /metrics
    static_configs:
      - targets: ["semantic-router:9190"]
        labels:
          service: semantic-router
          env: dev

  # Optional: Envoy
  - job_name: envoy
    metrics_path: /stats/prometheus
    static_configs:
      - targets: ["envoy-proxy:19000"]
        labels:
          service: envoy
          env: dev