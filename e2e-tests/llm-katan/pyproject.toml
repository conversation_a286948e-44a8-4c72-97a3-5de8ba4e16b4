[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "llm-katan"
version = "0.1.8"
description = "LLM Katan - Lightweight LLM Server for Testing - Real tiny models with FastAPI and HuggingFace"
readme = "README.md"
authors = [
    {name = "<PERSON><PERSON>va<PERSON>", email = "<EMAIL>"}
]
maintainers = [
    {name = "<PERSON><PERSON> Ovadia", email = "<EMAIL>"}
]
license = {text = "Apache-2.0"}
requires-python = ">=3.8"
keywords = ["llm", "testing", "fastapi", "huggingface", "vllm", "ai", "ml"]
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: Apache Software License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Software Development :: Testing",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
]

dependencies = [
    "fastapi>=0.104.0",
    "uvicorn[standard]>=0.24.0",
    "transformers>=4.35.0",
    "torch>=2.0.0",
    "click>=8.0.0",
    "pydantic>=2.0.0",
    "numpy>=1.21.0",
]

[project.optional-dependencies]
vllm = [
    "vllm>=0.2.0",
]
dev = [
    "pytest>=7.0.0",
    "pytest-asyncio>=0.21.0",
    "httpx>=0.24.0",
    "black>=23.0.0",
    "isort>=5.12.0",
    "flake8>=6.0.0",
]

[project.urls]
Homepage = "https://github.com/vllm-project/semantic-router"
Documentation = "https://github.com/vllm-project/semantic-router/tree/main/e2e-tests/llm-katan"
Repository = "https://github.com/vllm-project/semantic-router.git"
Issues = "https://github.com/vllm-project/semantic-router/issues"

[project.scripts]
llm-katan = "llm_katan.cli:main"

[tool.setuptools.packages.find]
where = ["."]

[tool.black]
line-length = 100
target-version = ['py38', 'py39', 'py310', 'py311', 'py312']

[tool.isort]
profile = "black"
line_length = 100