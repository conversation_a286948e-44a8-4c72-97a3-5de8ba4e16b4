"""
LLM Katan - Lightweight LLM Server for Testing

A lightweight LLM serving package using FastAPI and HuggingFace transformers,
designed for testing and development with real tiny models.
<PERSON><PERSON> (קטן) means "small" in Hebrew.

Signed-off-by: <PERSON><PERSON> <<EMAIL>>
"""

__version__ = "0.1.4"
__author__ = "Yo<PERSON>"
__email__ = "<EMAIL>"

from .cli import main
from .model import ModelBackend
from .server import create_app

__all__ = ["create_app", "ModelBackend", "main"]
