# Rust
target/
**/*.rs.bk
Cargo.lock

# Python
*.pyc
*.pyo
*.pyd
*.pyw
*.pyz
__pycache__/
.venv/
pii_env/

# Python build artifacts
dist/
build/
*.egg-info/
*.whl
*.tar.gz

# Go
*.exe
*.exe~
*.dll
*.so
*.dylib
*.test
*.out
extproc-server

# IDE
.idea/
.vscode/
*.swp
*.swo

# Dependency directories
vendor/

# Environment variables
.env

# OS specific
.DS_Store
Thumbs.db

# Project specific
bin/

# Model files (too large for git)
*.pt
*.pth
*.bin
*.onnx
*.h5
*/trained_model/*.pt
*/trained_model/*.pth
*/trained_model/*.bin
*/trained_model/*.onnx
*/trained_model/*.h5
*/trained_model/*.json
*/trained_model/*.txt
*/models/*.pt
*/models/*.pth
*/models/*.bin
*/models/*.onnx
*/models/*.h5
*/models/*.json
*/models/*.txt
# Allow README files in model directories
!*/trained_model/README.md
!*/models/README.md
models/

# Added by Claude Task Master
# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
dev-debug.log
node_modules/
# Editor directories and files
.idea
.vscode
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
# Task files
tasks.json
tasks/ 
.cursor/
.roo/
.env.example
.taskmasterconfig
example_prd.txt
.roomodes
.windsurfrules
scripts/prd.txt
.env.taskmaster

website/build
.docusaurus
spec/
results/

.github/copilot*.md

# AI tooling regulation docs
# Ignore Copilot/Claude/Cursor rule docs anywhere in the repo
**/[Cc]opilot*.md
**/[Cc]laude*.md
**/[Cc]ursor*.md
.github/[Cc]laude*.md
.github/[Cc]ursor*.md

# Cursor editor rules files
.cursorrules
.cursorrules.*

# augment editor rules
.augment

# Claude Code configuration (should not be committed)
CLAUDE.md