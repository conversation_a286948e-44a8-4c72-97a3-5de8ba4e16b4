/**
 * Any CSS included here will be global. The classic template
 * bundles Infima by default. Infima is a CSS framework designed to
 * work well for content-centric websites.
 */

/* You can override the default Infima variables here. */
:root {
  --ifm-color-primary: #0969DA;
  --ifm-color-primary-dark: #0860CA;
  --ifm-color-primary-darker: #0757BA;
  --ifm-color-primary-darkest: #054A94;
  --ifm-color-primary-light: #1F73EA;
  --ifm-color-primary-lighter: #2E7EFF;
  --ifm-color-primary-lightest: #58A6FF;
  --ifm-code-font-size: 95%;
  --docusaurus-highlighted-code-line-bg: rgba(9, 105, 218, 0.1);

  /* Custom tech colors for light theme */
  --tech-primary-blue: #0969DA;
  --tech-accent-blue: #58A6FF;
  --tech-accent-green: #1A7F37;
  --tech-accent-purple: #8250DF;
  --tech-light-bg: #FFFFFF;
  --tech-surface-bg: #F6F8FA;
  --tech-card-bg: #FFFFFF;
  --tech-border: #D1D9E0;
  --tech-border-accent: rgba(9, 105, 218, 0.2);
  --tech-text-primary: #1F2328;
  --tech-text-secondary: #656D76;
  --tech-shadow: 0 8px 24px rgba(9, 105, 218, 0.12);
  --tech-gradient: linear-gradient(135deg, #F6F8FA 0%, #FFFFFF 50%, #F0F3F6 100%);

  /* Light theme overrides */
  --ifm-background-color: var(--tech-light-bg);
  --ifm-background-surface-color: var(--tech-surface-bg);
  --ifm-color-content: var(--tech-text-primary);
  --ifm-color-content-secondary: var(--tech-text-secondary);
  --ifm-navbar-background-color: rgba(255, 255, 255, 0.95);
  --ifm-footer-background-color: var(--tech-surface-bg);
}

/* Dark theme variables (if needed) */
[data-theme='dark'] {
  --ifm-color-primary: #58A6FF;
  --ifm-color-primary-dark: #3D8BFF;
  --ifm-color-primary-darker: #2E7EFF;
  --ifm-color-primary-darkest: #1F6FEB;
  --ifm-color-primary-light: #7BB8FF;
  --ifm-color-primary-lighter: #8CC5FF;
  --ifm-color-primary-lightest: #B6D7FF;
  --docusaurus-highlighted-code-line-bg: rgba(88, 166, 255, 0.2);

  /* Dark theme tech colors */
  --tech-primary-blue: #58A6FF;
  --tech-accent-blue: #7BB8FF;
  --tech-accent-green: #39D353;
  --tech-accent-purple: #A855F7;
  --tech-light-bg: #0D1117;
  --tech-surface-bg: #161B22;
  --tech-card-bg: #21262D;
  --tech-border: #30363D;
  --tech-border-accent: rgba(88, 166, 255, 0.3);
  --tech-text-primary: #F0F6FC;
  --tech-text-secondary: #8B949E;
  --tech-shadow: 0 8px 24px rgba(0, 0, 0, 0.4);
  --tech-gradient: linear-gradient(135deg, #0D1117 0%, #161B22 50%, #21262D 100%);
}

/* Global body styling - Light tech theme */
body {
  background: var(--tech-gradient);
  background-attachment: fixed;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  color: var(--tech-text-primary);
}

/* Navbar enhancements */
.navbar {
  border-bottom: 1px solid var(--tech-border);
  box-shadow: var(--tech-shadow);
}

.navbar__title {
  font-weight: 700;
  background: linear-gradient(45deg, var(--tech-primary-blue), var(--tech-accent-purple));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Sidebar enhancements */
.theme-doc-sidebar-container {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-right: 1px solid var(--tech-border);
}

.menu__link {
  border-radius: 8px;
  transition: all 0.3s ease;
}

.menu__link:hover {
  background: rgba(9, 105, 218, 0.08);
  color: var(--tech-primary-blue);
  transform: translateX(4px);
}

.menu__link--active {
  background: linear-gradient(90deg, rgba(9, 105, 218, 0.12), transparent);
  border-left: 3px solid var(--tech-primary-blue);
  color: var(--tech-primary-blue);
}

/* Content area enhancements */
.main-wrapper {
  background: transparent;
}

article {
  background: var(--tech-card-bg);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  border: 1px solid var(--tech-border);
  padding: 2rem;
  margin: 1rem 0;
  box-shadow: var(--tech-shadow);
}

/* Code blocks */
.prism-code {
  background: var(--tech-surface-bg) !important;
  border: 1px solid var(--tech-border);
  border-radius: 8px;
  box-shadow: var(--tech-shadow);
}

/* Headings */
h1, h2, h3, h4, h5, h6 {
  color: var(--tech-text-primary);
}

h1 {
  background: linear-gradient(45deg, var(--tech-primary-blue), var(--tech-accent-green));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

h2 {
  border-bottom: 2px solid var(--tech-border);
  padding-bottom: 0.5rem;
}

/* Links */
a {
  color: var(--tech-primary-blue);
  transition: all 0.3s ease;
}

a:hover {
  color: var(--tech-accent-green);
  text-decoration: none;
}

/* Buttons */
.button {
  border-radius: 8px;
  font-weight: 600;
  transition: all 0.3s ease;
  border: 1px solid transparent;
}

.button--primary {
  background: linear-gradient(45deg, var(--tech-primary-blue), var(--tech-accent-purple));
  border: none;
  box-shadow: var(--tech-shadow);
}

.button--primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 32px rgba(9, 105, 218, 0.25);
}

/* Cards and containers */
.card {
  background: var(--tech-card-bg);
  border: 1px solid var(--tech-border);
  border-radius: 12px;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.card:hover {
  transform: translateY(-4px);
  box-shadow: 0 16px 48px rgba(9, 105, 218, 0.15);
  border-color: var(--tech-border-accent);
}

/* Footer */
.footer {
  background: var(--tech-surface-bg);
  border-top: 1px solid var(--tech-border);
}

.footer__title {
  color: var(--tech-text-primary);
  font-weight: 600;
}

.footer__item {
  color: var(--tech-text-secondary);
}

.footer__link-item {
  color: var(--tech-text-secondary);
  transition: color 0.3s ease;
}

.footer__link-item:hover {
  color: var(--tech-primary-blue);
  text-decoration: none;
}

.footer__copyright {
  color: var(--tech-text-secondary);
}

/* Animations */
@keyframes glow {
  0%, 100% { box-shadow: 0 0 20px rgba(88, 166, 255, 0.3); }
  50% { box-shadow: 0 0 30px rgba(88, 166, 255, 0.6); }
}

.tech-glow {
  animation: glow 2s ease-in-out infinite;
}

/* Tech-specific enhancements */
.hero__title {
  background: linear-gradient(45deg, var(--tech-primary-blue), var(--tech-accent-green), var(--tech-accent-purple));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-size: 3.5rem;
  font-weight: 800;
}

.hero__subtitle {
  color: var(--tech-text-secondary);
  font-size: 1.25rem;
  margin-top: 1rem;
}

/* Mermaid diagram styling */
.mermaid {
  background: var(--tech-card-bg);
  border-radius: 12px;
  padding: 1rem;
  border: 1px solid var(--tech-border);
  box-shadow: var(--tech-shadow);
}

/* Mermaid light theme customization */
.mermaid .node rect,
.mermaid .node circle,
.mermaid .node ellipse,
.mermaid .node polygon {
  fill: var(--tech-surface-bg);
  stroke: var(--tech-primary-blue);
  stroke-width: 2px;
}

.mermaid .node .label {
  color: var(--tech-text-primary);
  font-weight: 500;
}

.mermaid .edgePath .path {
  stroke: var(--tech-primary-blue);
  stroke-width: 2px;
}

.mermaid .edgeLabel {
  background-color: var(--tech-card-bg);
  color: var(--tech-text-primary);
  border-radius: 4px;
  padding: 2px 6px;
}

/* Table styling */
table {
  background: var(--tech-card-bg);
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid var(--tech-border);
}

th {
  background: var(--tech-surface-bg);
  color: var(--tech-primary-blue);
  font-weight: 600;
}

td, th {
  border-color: var(--tech-border);
}

/* Admonition styling */
.admonition {
  background: var(--tech-card-bg);
  border-left: 4px solid var(--tech-primary-blue);
  border-radius: 0 8px 8px 0;
}

.admonition-heading {
  background: rgba(9, 105, 218, 0.08);
  color: var(--tech-primary-blue);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  article {
    margin: 0.5rem;
    padding: 1rem;
  }

  .navbar__title {
    font-size: 1.2rem;
  }

  .hero__title {
    font-size: 2.5rem;
  }

  /* Mobile navbar fixes */
  .navbar {
    position: sticky;
    top: 0;
    z-index: 1000;
    background: rgba(255, 255, 255, 0.98);
  }

  /* Keep only essential mobile fixes */
  .navbar {
    position: sticky;
    top: 0;
    z-index: 1000;
  }

  /* Ensure hero content is not hidden behind navbar */
  .hero {
    margin-top: 0;
    padding-top: 2rem;
  }
}
