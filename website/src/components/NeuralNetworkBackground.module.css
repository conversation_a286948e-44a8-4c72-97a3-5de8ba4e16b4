.neuralNetworkContainer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  pointer-events: none;
  z-index: 0;
}

.neuralNetworkCanvas {
  width: 100%;
  height: 100%;
  display: block;
  opacity: 0.6;
  transition: opacity 0.3s ease;
}

/* Enhance visibility on hover */
.neuralNetworkContainer:hover .neuralNetworkCanvas {
  opacity: 0.8;
}

/* Responsive adjustments */
@media screen and (max-width: 768px) {
  .neuralNetworkCanvas {
    opacity: 0.4;
  }
  
  .neuralNetworkContainer:hover .neuralNetworkCanvas {
    opacity: 0.6;
  }
}

@media (prefers-reduced-motion: reduce) {
  .neuralNetworkCanvas {
    opacity: 0.3;
  }
}
