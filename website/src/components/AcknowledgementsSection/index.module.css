/* Acknowledgements Section Styles */
.acknowledgementsSection {
  padding: 4rem 0;
  background: linear-gradient(135deg, #F6F8FA 0%, #FFFFFF 50%, #F0F3F6 100%);
  position: relative;
  overflow: hidden;
}

.acknowledgementsSection::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(circle at 25% 25%, rgba(88, 166, 255, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(253, 181, 22, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 50% 50%, rgba(130, 80, 223, 0.03) 0%, transparent 50%);
  pointer-events: none;
  animation: acknowledgementsBackgroundFlow 15s ease-in-out infinite;
}

.acknowledgementsContainer {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  position: relative;
  z-index: 1;
  text-align: center;
}

.acknowledgementsTitle {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  background: linear-gradient(135deg, #0969DA, #FDB516, #8250DF);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: acknowledgementsTitleGlow 4s ease-in-out infinite;
}

.acknowledgementsSubtitle {
  font-size: 1.2rem;
  line-height: 1.6;
  color: #656D76;
  margin-bottom: 3rem;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

.projectsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 2rem;
  margin-top: 2rem;
}

.projectCard {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 1.5rem;
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(88, 166, 255, 0.2);
  border-radius: 16px;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  box-shadow: 0 4px 16px rgba(9, 105, 218, 0.08);
  text-decoration: none;
  color: inherit;
}

.projectCard:hover {
  background: rgba(255, 255, 255, 0.95);
  border-color: rgba(88, 166, 255, 0.4);
  transform: translateY(-4px);
  box-shadow: 0 8px 32px rgba(9, 105, 218, 0.15);
  text-decoration: none;
  color: inherit;
}

.projectLogoWrapper {
  width: 80px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1rem;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.9);
  box-shadow: 0 2px 8px rgba(9, 105, 218, 0.1);
}

.projectLogo {
  max-width: 60px;
  max-height: 60px;
  width: auto;
  height: auto;
  object-fit: contain;
  transition: transform 0.3s ease;
}

.projectCard:hover .projectLogo {
  transform: scale(1.1);
}

.projectName {
  font-size: 0.9rem;
  font-weight: 600;
  color: #1F2328;
  text-align: center;
  line-height: 1.4;
}

/* Animations */
@keyframes acknowledgementsBackgroundFlow {
  0%, 100% {
    transform: translateX(0) translateY(0);
  }
  33% {
    transform: translateX(5px) translateY(-3px);
  }
  66% {
    transform: translateX(-3px) translateY(5px);
  }
}

@keyframes acknowledgementsTitleGlow {
  0%, 100% {
    filter: drop-shadow(0 0 12px rgba(253, 181, 22, 0.4));
  }
  33% {
    filter: drop-shadow(0 0 16px rgba(48, 162, 255, 0.6));
  }
  66% {
    filter: drop-shadow(0 0 14px rgba(168, 85, 247, 0.5));
  }
}

/* Responsive Design */
@media screen and (max-width: 768px) {
  .acknowledgementsSection {
    padding: 3rem 0;
  }

  .acknowledgementsContainer {
    padding: 0 1rem;
  }

  .acknowledgementsTitle {
    font-size: 2rem;
    margin-bottom: 0.8rem;
  }

  .acknowledgementsSubtitle {
    font-size: 1.1rem;
    margin-bottom: 2rem;
  }

  .projectsGrid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1.5rem;
  }

  .projectCard {
    padding: 1rem;
  }

  .projectLogoWrapper {
    width: 60px;
    height: 60px;
    margin-bottom: 0.8rem;
  }

  .projectLogo {
    max-width: 45px;
    max-height: 45px;
  }

  .projectName {
    font-size: 0.8rem;
  }
}
