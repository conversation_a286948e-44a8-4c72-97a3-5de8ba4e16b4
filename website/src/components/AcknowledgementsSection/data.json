{"title": "Acknowledgements", "subtitle": "vLLM Semantic Router is born in open source and built on open source ❤️", "projects": [{"id": "vLLM", "name": "vLLM", "logo": "/img/acknowledgements/vllm-logo.png", "url": "https://github.com/vllm-project/vllm", "description": "A high-throughput and memory-efficient inference and serving engine for LLMs"}, {"id": "huggingFace-candle", "name": "<PERSON><PERSON><PERSON><PERSON>", "logo": "/img/acknowledgements/huggingface-logo.svg", "url": "https://github.com/huggingface/candle", "description": "Minimalist ML framework for Rust"}, {"id": "envoyproxy", "name": "EnvoyProxy", "logo": "/img/acknowledgements/envoy-logo.png", "url": "https://github.com/envoyproxy/envoy", "description": "Cloud-native high-performance edge/middle/service proxy"}, {"id": "kubernetes", "name": "Kubernetes", "logo": "/img/acknowledgements/k8s-logo.png", "url": "https://github.com/kubernetes", "description": "Production-Grade Container Scheduling and Management"}]}