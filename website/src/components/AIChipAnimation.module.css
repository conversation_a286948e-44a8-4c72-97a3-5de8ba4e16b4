.chipContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 16px;
  border: 1px solid rgba(88, 166, 255, 0.2);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  max-width: 400px;
  margin: 0 auto;
  box-shadow: 0 8px 32px rgba(9, 105, 218, 0.1);
}

.chipContainer:hover {
  transform: translateY(-4px);
  box-shadow: 0 16px 48px rgba(9, 105, 218, 0.15);
  border-color: rgba(88, 166, 255, 0.3);
}

.chipSvg {
  width: 100%;
  height: auto;
  max-width: 350px;
  filter: drop-shadow(0 4px 16px rgba(9, 105, 218, 0.2));
}

.chipBase {
  animation: chipPulse 3s ease-in-out infinite;
}

.circuitGroup .circuit-path {
  stroke-dasharray: 10 5;
  animation: circuitFlow 2s linear infinite;
}

.coresGroup .processingCore {
  animation: coreProcess 1.5s ease-in-out infinite alternate;
  transform-origin: center;
}

.coreLabel {
  font-family: 'Monaco', '<PERSON><PERSON>', 'Ubuntu Mono', monospace;
  font-size: 8px;
  font-weight: bold;
  fill: #ffffff;
  text-shadow: 0 0 4px rgba(255, 255, 255, 0.5);
}

.dataFlowGroup .data-point {
  animation: dataFlow 1s ease-in-out infinite;
  transform-origin: center;
}

.pinsGroup rect {
  animation: pinActivity 2s ease-in-out infinite;
}

.chipLabel {
  margin-top: 1.5rem;
  text-align: center;
}

.chipTitle {
  display: block;
  font-size: 1.2rem;
  font-weight: 700;
  color: #1F2328;
  margin-bottom: 0.5rem;
  background: linear-gradient(45deg, #0969DA, #8250DF);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.chipSubtitle {
  display: block;
  font-size: 0.9rem;
  color: #656D76;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  letter-spacing: 1px;
}

/* Animations */
@keyframes chipPulse {
  0%, 100% {
    filter: brightness(1) drop-shadow(0 0 10px rgba(88, 166, 255, 0.3));
  }
  50% {
    filter: brightness(1.1) drop-shadow(0 0 20px rgba(88, 166, 255, 0.5));
  }
}

@keyframes circuitFlow {
  0% {
    stroke-dashoffset: 0;
    stroke: #58A6FF;
  }
  50% {
    stroke: #FDB516;
  }
  100% {
    stroke-dashoffset: -15;
    stroke: #58A6FF;
  }
}

@keyframes coreProcess {
  0% {
    transform: scale(1);
    filter: brightness(1);
  }
  100% {
    transform: scale(1.1);
    filter: brightness(1.3) drop-shadow(0 0 8px rgba(253, 181, 22, 0.6));
  }
}

@keyframes dataFlow {
  0%, 100% {
    transform: scale(1);
    opacity: 0.7;
  }
  50% {
    transform: scale(1.5);
    opacity: 1;
    filter: drop-shadow(0 0 6px currentColor);
  }
}

@keyframes pinActivity {
  0%, 100% {
    opacity: 0.6;
    transform: scaleY(1);
  }
  50% {
    opacity: 1;
    transform: scaleY(1.2);
    filter: drop-shadow(0 0 4px #8CC5FF);
  }
}

/* Responsive design */
@media screen and (max-width: 768px) {
  .chipContainer {
    padding: 1.5rem;
    max-width: 300px;
  }
  
  .chipSvg {
    max-width: 280px;
  }
  
  .chipTitle {
    font-size: 1rem;
  }
  
  .chipSubtitle {
    font-size: 0.8rem;
  }
}

@media screen and (max-width: 480px) {
  .chipContainer {
    padding: 1rem;
    max-width: 250px;
  }
  
  .chipSvg {
    max-width: 220px;
  }
  
  .chipTitle {
    font-size: 0.9rem;
  }
  
  .chipSubtitle {
    font-size: 0.7rem;
  }
}

/* Reduce motion for accessibility */
@media (prefers-reduced-motion: reduce) {
  .chipBase,
  .circuit-path,
  .processingCore,
  .data-point,
  .pinsGroup rect {
    animation: none;
  }
  
  .chipContainer:hover {
    transform: none;
  }
}
