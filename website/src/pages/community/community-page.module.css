.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
}

.header {
  text-align: center;
  margin-bottom: 3rem;
}

.header h1 {
  font-size: 3rem;
  color: var(--ifm-color-primary);
  margin-bottom: 1rem;
  background: linear-gradient(45deg, #0969da, #8250df);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.subtitle {
  font-size: 1.25rem;
  color: var(--ifm-color-content-secondary);
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

.main {
  display: flex;
  flex-direction: column;
  gap: 3rem;
}

.section {
  margin-bottom: 2rem;
}

.section h2 {
  color: var(--ifm-color-primary);
  margin-bottom: 1.5rem;
  font-size: 2rem;
}

.card {
  background: var(--ifm-background-surface-color);
  border: 1px solid var(--ifm-color-emphasis-200);
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.card h3 {
  color: var(--ifm-color-primary);
  margin-bottom: 1rem;
}

.card h4 {
  color: var(--ifm-color-primary);
  margin-bottom: 0.5rem;
}

.card p {
  line-height: 1.6;
  margin-bottom: 1rem;
}

.card ul {
  padding-left: 1.5rem;
  line-height: 1.6;
}

.card li {
  margin-bottom: 0.5rem;
}

.grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.contributeGrid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 2rem;
}

.codeBlock {
  background: linear-gradient(135deg, #F6F8FA 0%, #FFFFFF 50%, #F0F3F6 100%);
  border-radius: 16px;
  box-shadow:
    0 20px 60px rgba(9, 105, 218, 0.15),
    0 8px 32px rgba(88, 166, 255, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
  transition: all 0.4s ease;
  border: 2px solid;
  border-image: linear-gradient(45deg, #58A6FF, #FDB516, #A855F7) 1;
  overflow: hidden;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  position: relative;
  margin: 1rem 0;
  max-width: 100%;
}

.codeBlock::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(88, 166, 255, 0.1), rgba(253, 181, 22, 0.1), rgba(168, 85, 247, 0.1));
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.codeBlock:hover::before {
  opacity: 1;
}

.codeBlock:hover {
  transform: translateY(-6px) scale(1.03);
  box-shadow:
    0 32px 80px rgba(9, 105, 218, 0.25),
    0 16px 40px rgba(88, 166, 255, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.codeHeader {
  background: linear-gradient(90deg, #F0F3F6 0%, #F6F8FA 50%, #F0F3F6 100%);
  padding: 1rem 1.5rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid rgba(88, 166, 255, 0.2);
  position: relative;
}

.codeHeader::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent 0%, #58A6FF 50%, transparent 100%);
}

.windowControls {
  display: flex;
  gap: 0.5rem;
}

.controlButton {
  width: 14px;
  height: 14px;
  border-radius: 50%;
  display: block;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.controlButton:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
}

.controlButton:nth-child(1) {
  background: radial-gradient(circle, #ff6b6b, #ff5f57);
}

.controlButton:nth-child(2) {
  background: radial-gradient(circle, #ffd93d, #ffbd2e);
}

.controlButton:nth-child(3) {
  background: radial-gradient(circle, #6bcf7f, #28ca42);
}

.title {
  color: #1F2328;
  font-size: 0.9rem;
  font-weight: 600;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  background: linear-gradient(45deg, #0969DA, #FDB516);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 0 10px rgba(9, 105, 218, 0.2);
  letter-spacing: 0.5px;
}

.codeContent {
  padding: 2rem;
  background: linear-gradient(135deg, #F6F8FA 0%, #FFFFFF 50%, #F0F3F6 100%) !important;
  overflow-x: auto;
  position: relative;
}

.codeText {
  margin: 0;
  font-size: 0.875rem;
  line-height: 1.6;
  white-space: pre;
  overflow-x: auto;
  color: #1F2328 !important;
  background: transparent !important;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  text-align: left;
}

.steps {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.step {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
}

.stepNumber {
  background: var(--ifm-color-primary);
  color: white;
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  flex-shrink: 0;
  margin-top: 0.25rem;
}

.stepNumberTips {
  display: flex;
  flex-direction: column;
  margin: 0 0 0 1rem;
}

.stepNumberTips p {
    line-height: 1.5;
}

.step h4 {
  margin: 0 0 0.5rem 0;
  color: var(--ifm-color-primary);
}

.step p {
  margin: 0;
  color: var(--ifm-color-content-secondary);
  line-height: 1.5;
}

.tagGrid {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
  margin-top: 1rem;
}

.tag {
  background: var(--ifm-color-primary-lightest);
  color: var(--ifm-color-primary-dark);
  padding: 0.375rem 0.75rem;
  border-radius: 12px;
  font-size: 0.875rem;
  font-weight: 500;
  border: 1px solid var(--ifm-color-primary-light);
}

.enforcementGrid {
  display: grid;
  gap: 1.5rem;
  margin-top: 1rem;
}

.enforcementItem {
  padding: 1.5rem;
  background: var(--ifm-color-emphasis-100);
  border-radius: 12px;
  border-left: 4px solid var(--ifm-color-primary);
}

.enforcementItem h4 {
  margin: 0 0 1rem 0;
  color: var(--ifm-color-primary);
  font-size: 1.125rem;
}

.enforcementItem p {
  margin: 0 0 0.75rem 0;
  line-height: 1.5;
}

.enforcementItem p:last-child {
  margin-bottom: 0;
}

/* Links */
a {
  color: var(--ifm-color-primary);
  text-decoration: none;
  font-weight: 500;
}

a:hover {
  text-decoration: underline;
}

/* Responsive design */
@media (max-width: 768px) {
  .container {
    padding: 1rem;
  }
  
  .header h1 {
    font-size: 2rem;
  }
  
  .subtitle {
    font-size: 1.125rem;
  }
  
  .grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .contributeGrid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  
  .card {
    padding: 1.5rem;
  }
  
  .step {
    flex-direction: column;
    text-align: center;
  }
  
  .stepNumber {
    align-self: center;
  }
  
  .codeBlock {
    border-width: 1px;
    margin: 0.5rem 0;
  }

  .codeText {
    font-size: 0.75rem;
    line-height: 1.4;
  }

  .codeContent {
    padding: 1.5rem;
  }

  .codeHeader {
    padding: 0.75rem 1rem;
  }

  .title {
    font-size: 0.8rem;
  }

  .controlButton {
    width: 10px;
    height: 10px;
  }
}

@media (max-width: 480px) {
  .codeText {
    font-size: 0.7rem;
  }

  .codeContent {
    padding: 1rem;
  }

  .codeHeader {
    padding: 0.5rem 0.75rem;
  }

  .title {
    font-size: 0.75rem;
  }
}

/* Dark mode adjustments */
[data-theme='dark'] .card {
  background: var(--ifm-background-surface-color);
  border-color: var(--ifm-color-emphasis-300);
}

[data-theme='dark'] .enforcementItem {
  background: var(--ifm-color-emphasis-200);
}

[data-theme='dark'] .tag {
  background: var(--ifm-color-primary-darkest);
  color: var(--ifm-color-primary-lightest);
  border-color: var(--ifm-color-primary-dark);
}

[data-theme='dark'] .codeBlock {
  background: linear-gradient(135deg, #161b22 0%, #0d1117 50%, #161b22 100%);
  border-image: linear-gradient(45deg, #58A6FF, #FDB516, #A855F7) 1;
  box-shadow:
    0 20px 60px rgba(88, 166, 255, 0.15),
    0 8px 32px rgba(88, 166, 255, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

[data-theme='dark'] .codeHeader {
  background: linear-gradient(90deg, #161b22 0%, #21262d 50%, #161b22 100%);
  border-bottom: 1px solid rgba(88, 166, 255, 0.3);
}

[data-theme='dark'] .codeContent {
  background: linear-gradient(135deg, #161b22 0%, #0d1117 50%, #161b22 100%) !important;
}

[data-theme='dark'] .codeText {
  color: #e6edf3 !important;
}

[data-theme='dark'] .title {
  color: #e6edf3;
}
