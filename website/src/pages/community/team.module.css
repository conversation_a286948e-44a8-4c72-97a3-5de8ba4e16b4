.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
}

.header {
  text-align: center;
  margin-bottom: 3rem;
}

.header h1 {
  font-size: 3rem;
  color: var(--ifm-color-primary);
  margin-bottom: 1rem;
  background: linear-gradient(45deg, #0969da, #8250df);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.subtitle {
  font-size: 1.25rem;
  color: var(--ifm-color-content-secondary);
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

.main {
  display: flex;
  flex-direction: column;
  gap: 4rem;
}

.section {
  margin-bottom: 2rem;
}

.section h2 {
  color: var(--ifm-color-primary);
  margin-bottom: 1rem;
  font-size: 2rem;
  text-align: center;
}

.sectionDescription {
  text-align: center;
  color: var(--ifm-color-content-secondary);
  font-size: 1.125rem;
  margin-bottom: 2rem;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.teamGrid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 2rem;
  margin-top: 2rem;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

.joinTeamGrid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 2rem;
  margin-top: 2rem;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

.joinTeamGrid .memberCard {
  grid-column: 1 / -1;
}

.memberCard {
  background: var(--ifm-background-surface-color);
  border: 1px solid var(--ifm-color-emphasis-200);
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.memberCard:hover {
  transform: translateY(-8px);
  box-shadow: 0 16px 32px rgba(0, 0, 0, 0.15);
}

.contributorCard {
  border: 2px dashed var(--ifm-color-primary);
  background: linear-gradient(135deg, var(--ifm-color-primary-lightest) 0%, var(--ifm-background-surface-color) 100%);
}

.memberHeader {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.avatar {
  width: 64px;
  height: 64px;
  border-radius: 50%;
  border: 3px solid var(--ifm-color-primary-light);
  object-fit: cover;
}

.memberInfo {
  flex: 1;
}

.memberName {
  margin: 0 0 0.25rem 0;
  color: var(--ifm-color-primary);
  font-size: 1.25rem;
  font-weight: 700;
}

.memberRole {
  margin: 0;
  color: var(--ifm-color-content-secondary);
  font-size: 0.875rem;
  font-weight: 500;
  line-height: 1.4;
}

.company {
  color: var(--ifm-color-primary);
  font-weight: 600;
}

.memberBio {
  color: var(--ifm-color-content);
  line-height: 1.6;
  margin-bottom: 1.5rem;
  flex-grow: 1;
}

.expertise {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
}

.skillTag {
  background: var(--ifm-color-primary-lightest);
  color: var(--ifm-color-primary-dark);
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
  border: 1px solid var(--ifm-color-primary-light);
}

.memberActions {
  margin-top: auto;
  display: flex;
  gap: 0.75rem;
  flex-wrap: wrap;
}

.actionLink {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  background: var(--ifm-color-emphasis-200);
  color: var(--ifm-color-primary);
  padding: 0.5rem 1rem;
  border-radius: 6px;
  text-decoration: none;
  font-weight: 500;
  font-size: 0.875rem;
  transition: all 0.3s ease;
  flex: 1;
  justify-content: center;
  min-width: 100px;
}

.actionLink:hover {
  background: var(--ifm-color-primary);
  color: white;
  text-decoration: none;
  transform: translateY(-2px);
}

.joinButton {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  background: var(--ifm-color-primary);
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  text-decoration: none;
  font-weight: 600;
  transition: all 0.3s ease;
  width: 100%;
  justify-content: center;
}

.joinButton:hover {
  background: var(--ifm-color-primary-dark);
  color: white;
  text-decoration: none;
  transform: translateY(-2px);
}

.recognitionCard {
  background: var(--ifm-background-surface-color);
  border: 1px solid var(--ifm-color-emphasis-200);
  border-radius: 16px;
  padding: 2.5rem;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.recognitionCard h3 {
  color: var(--ifm-color-primary);
  margin-bottom: 1rem;
  font-size: 1.5rem;
}

.recognitionCard h4 {
  color: var(--ifm-color-primary);
  margin: 2rem 0 1rem 0;
  font-size: 1.25rem;
}

.pathToMaintainer {
  margin-top: 2rem;
}

.steps {
  display: grid;
  gap: 1.5rem;
  margin-top: 1rem;
}

.step {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1.5rem;
  background: var(--ifm-color-emphasis-100);
  border-radius: 12px;
  border-left: 4px solid var(--ifm-color-primary);
}

.stepNumber {
  background: var(--ifm-color-primary);
  color: white;
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  flex-shrink: 0;
  margin-top: 0.25rem;
}

.step h5 {
  margin: 0 0 0.5rem 0;
  color: var(--ifm-color-primary);
  font-size: 1rem;
}

.step p {
  margin: 0;
  color: var(--ifm-color-content-secondary);
  line-height: 1.5;
}

.involvementGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
}

.involvementCard {
  background: var(--ifm-background-surface-color);
  border: 1px solid var(--ifm-color-emphasis-200);
  border-radius: 16px;
  padding: 2rem;
  text-align: center;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.involvementCard:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.involvementCard h3 {
  color: var(--ifm-color-primary);
  margin-bottom: 1rem;
  font-size: 1.25rem;
}

.involvementCard p {
  color: var(--ifm-color-content-secondary);
  margin-bottom: 1.5rem;
  line-height: 1.6;
}

.actionButton {
  display: inline-block;
  background: var(--ifm-color-primary);
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  text-decoration: none;
  font-weight: 600;
  transition: all 0.3s ease;
}

.actionButton:hover {
  background: var(--ifm-color-primary-dark);
  color: white;
  text-decoration: none;
  transform: translateY(-2px);
}

/* Responsive design */
@media (max-width: 768px) {
  .container {
    padding: 1rem;
  }
  
  .header h1 {
    font-size: 2rem;
  }
  
  .subtitle {
    font-size: 1.125rem;
  }
  
  .teamGrid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
    max-width: none;
  }

  .joinTeamGrid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
    max-width: none;
  }
  
  .memberHeader {
    flex-direction: column;
    text-align: center;
  }
  
  .steps {
    gap: 1rem;
  }
  
  .step {
    flex-direction: column;
    text-align: center;
    padding: 1rem;
  }
  
  .stepNumber {
    align-self: center;
  }
  
  .involvementGrid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
}

/* Dark mode adjustments */
[data-theme='dark'] .memberCard,
[data-theme='dark'] .recognitionCard,
[data-theme='dark'] .involvementCard {
  background: var(--ifm-background-surface-color);
  border-color: var(--ifm-color-emphasis-300);
}

[data-theme='dark'] .contributorCard {
  background: linear-gradient(135deg, var(--ifm-color-primary-darkest) 0%, var(--ifm-background-surface-color) 100%);
  border-color: var(--ifm-color-primary);
}

[data-theme='dark'] .step {
  background: var(--ifm-color-emphasis-200);
}

[data-theme='dark'] .skillTag {
  background: var(--ifm-color-primary-darkest);
  color: var(--ifm-color-primary-lightest);
  border-color: var(--ifm-color-primary-dark);
}
