/**
 * CSS files with the .module.css suffix will be treated as CSS modules
 * and scoped locally.
 */

.heroBanner {
  padding: 5rem 0;
  text-align: center;
  position: relative;
  overflow: hidden;
  background: linear-gradient(135deg, #F6F8FA 0%, #FFFFFF 25%, #F0F3F6 50%, #FFFFFF 75%, #F6F8FA 100%);
  border-bottom: 1px solid var(--tech-border);
  min-height: 80vh;
}

.heroBanner::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 30%, rgba(88, 166, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 70%, rgba(253, 181, 22, 0.08) 0%, transparent 50%),
    radial-gradient(circle at 50% 50%, rgba(130, 80, 223, 0.06) 0%, transparent 70%);
  pointer-events: none;
  animation: backgroundPulse 8s ease-in-out infinite;
}

.heroContent {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 4rem;
  margin-bottom: 3rem;
}

.heroLeft {
  flex: 1;
  text-align: left;
}

.heroRight {
  flex: 1;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.heroTitle {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 1.5rem;
  margin-bottom: 1.5rem;
  flex-wrap: wrap;
}

.vllmLogo {
  height: 80px;
  width: auto;
  filter: drop-shadow(0 4px 16px rgba(9, 105, 218, 0.3));
  transition: all 0.3s ease;
}

.vllmLogo:hover {
  transform: scale(1.05);
  filter: drop-shadow(0 8px 24px rgba(9, 105, 218, 0.4));
}

.codeImage {
  max-width: 100%;
  height: auto;
  max-height: 400px;
  border-radius: 12px;
  box-shadow:
    0 16px 48px rgba(9, 105, 218, 0.15),
    0 8px 24px rgba(9, 105, 218, 0.1);
  transition: all 0.3s ease;
  border: 1px solid rgba(9, 105, 218, 0.1);
}

.codeImage:hover {
  transform: translateY(-4px) scale(1.02);
  box-shadow:
    0 24px 64px rgba(9, 105, 218, 0.2),
    0 12px 32px rgba(9, 105, 218, 0.15);
}

.buttons {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 2rem;
}

.buttons .button {
  margin: 0 0.5rem;
  background: linear-gradient(45deg, var(--tech-primary-blue), var(--tech-accent-purple));
  border: none;
  box-shadow: 0 4px 16px rgba(9, 105, 218, 0.2);
  transition: all 0.3s ease;
}

.buttons .button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(9, 105, 218, 0.3);
}

/* New AI-themed styles */
.aiGlow {
  background: linear-gradient(45deg, #FDB516, #30A2FF, #58A6FF);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: aiTextGlow 3s ease-in-out infinite;
}

.techBadges {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-top: 1rem;
  justify-content: flex-start;
}

.techBadge {
  background: rgba(88, 166, 255, 0.1);
  border: 1px solid rgba(88, 166, 255, 0.3);
  border-radius: 20px;
  padding: 0.3rem 0.8rem;
  font-size: 0.8rem;
  font-weight: 600;
  color: #58A6FF;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.techBadge:hover {
  background: rgba(88, 166, 255, 0.2);
  border-color: rgba(88, 166, 255, 0.5);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(88, 166, 255, 0.2);
}



/* Flow Diagram Section */
.flowSection {
  padding: 2.5rem 0;
  background: linear-gradient(135deg, #FFFFFF 0%, #F6F8FA 50%, #FFFFFF 100%);
  position: relative;
  overflow: hidden;
}

.flowSection::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(circle at 20% 20%, rgba(9, 105, 218, 0.03) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(9, 105, 218, 0.03) 0%, transparent 50%),
    radial-gradient(circle at 40% 60%, rgba(9, 105, 218, 0.02) 0%, transparent 50%);
  pointer-events: none;
}

.flowContainer {
  position: relative;
  z-index: 1;
}

/* Architecture Container */
.architectureContainer {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  text-align: center;
}

.architectureTitle {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 2rem;
  color: var(--ifm-color-primary);
  background: linear-gradient(135deg, var(--ifm-color-primary), var(--ifm-color-secondary));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.architectureImageWrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 2rem;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 16px;
  border: 1px solid rgba(88, 166, 255, 0.1);
  backdrop-filter: blur(10px);
  box-shadow: 0 8px 32px rgba(9, 105, 218, 0.1);
}

.architectureImage {
  max-width: 100%;
  height: auto;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(9, 105, 218, 0.15);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.architectureImage:hover {
  transform: scale(1.02);
  box-shadow: 0 8px 32px rgba(9, 105, 218, 0.2);
}

/* AI Tech Showcase Section */
.aiTechSection {
  padding: 4rem 0;
  background: linear-gradient(135deg, #F6F8FA 0%, #FFFFFF 50%, #F0F3F6 100%);
  position: relative;
  overflow: hidden;
}

.aiTechSection::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(circle at 30% 20%, rgba(253, 181, 22, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 70% 80%, rgba(88, 166, 255, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 50% 50%, rgba(130, 80, 223, 0.03) 0%, transparent 50%);
  pointer-events: none;
  animation: aiBackgroundFlow 12s ease-in-out infinite;
}

.aiTechContainer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 4rem;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  position: relative;
  z-index: 1;
}

.aiTechLeft {
  flex: 1;
  max-width: 500px;
}

.aiTechRight {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}

.aiTechTitle {
  font-size: 2.5rem;
  font-weight: 800;
  margin-bottom: 1.5rem;
  background: linear-gradient(45deg, #0969DA, #FDB516, #8250DF);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: aiTitleGlow 4s ease-in-out infinite;
}

.aiTechDescription {
  font-size: 1.1rem;
  line-height: 1.7;
  color: #656D76;
  margin-bottom: 2rem;
}

.aiFeatures {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.aiFeature {
  display: flex;
  align-items: center;
  gap: 0.8rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(88, 166, 255, 0.2);
  border-radius: 12px;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  box-shadow: 0 4px 16px rgba(9, 105, 218, 0.08);
}

.aiFeature:hover {
  background: rgba(255, 255, 255, 0.95);
  border-color: rgba(88, 166, 255, 0.4);
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(9, 105, 218, 0.15);
}

.aiFeatureIcon {
  font-size: 1.5rem;
  filter: drop-shadow(0 0 8px rgba(9, 105, 218, 0.3));
}

.aiFeature span:last-child {
  color: #1F2328;
  font-weight: 600;
  font-size: 0.95rem;
}











/* Responsive Design */
@media screen and (max-width: 996px) {
  .architectureContainer {
    padding: 1rem;
  }

  .architectureTitle {
    font-size: 2rem;
    margin-bottom: 1.5rem;
  }

  .architectureImageWrapper {
    padding: 1rem;
  }
}

/* Connection Lines Enhancement */
.connectionSection {
  position: relative;
  height: 60px;
  overflow: hidden;
  background: linear-gradient(90deg, transparent 0%, rgba(88, 166, 255, 0.1) 50%, transparent 100%);
}

.connectionLines {
  position: relative;
  width: 100%;
  height: 100%;
}

.connectionLine {
  position: absolute;
  height: 2px;
  background: linear-gradient(90deg, transparent 0%, #58A6FF 20%, #FDB516 50%, #A855F7 80%, transparent 100%);
  animation: connectionFlow 4s linear infinite;
}

.connectionLine1 {
  top: 20%;
  width: 60%;
  left: 20%;
  animation-delay: 0s;
}

.connectionLine2 {
  top: 50%;
  width: 80%;
  left: 10%;
  animation-delay: 1s;
}

.connectionLine3 {
  top: 80%;
  width: 70%;
  left: 15%;
  animation-delay: 2s;
}

.connectionLine4 {
  top: 30%;
  width: 50%;
  left: 25%;
  animation-delay: 0.5s;
}

.connectionLine5 {
  top: 70%;
  width: 65%;
  left: 17.5%;
  animation-delay: 1.5s;
}

/* Animations */
@keyframes backgroundPulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

@keyframes aiTextGlow {
  0%, 100% {
    filter: drop-shadow(0 0 8px rgba(253, 181, 22, 0.5));
  }
  50% {
    filter: drop-shadow(0 0 16px rgba(48, 162, 255, 0.7));
  }
}

@keyframes aiTitleGlow {
  0%, 100% {
    filter: drop-shadow(0 0 12px rgba(253, 181, 22, 0.4));
  }
  33% {
    filter: drop-shadow(0 0 16px rgba(48, 162, 255, 0.6));
  }
  66% {
    filter: drop-shadow(0 0 14px rgba(168, 85, 247, 0.5));
  }
}

@keyframes aiBackgroundFlow {
  0%, 100% {
    transform: translateX(0) translateY(0);
  }
  33% {
    transform: translateX(10px) translateY(-5px);
  }
  66% {
    transform: translateX(-5px) translateY(10px);
  }
}

@keyframes connectionFlow {
  0% {
    transform: translateX(-100%);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateX(100vw);
    opacity: 0;
  }
}

/* Mobile Banner Optimization */
@media screen and (max-width: 768px) {
  .heroBanner {
    padding: 3rem 0;
    min-height: 60vh;
  }

  .heroContent {
    flex-direction: column;
    gap: 2rem;
    text-align: center;
  }

  .heroLeft {
    flex: none;
    width: 100%;
    text-align: center;
  }

  .heroRight {
    flex: none;
    width: 100%;
    justify-content: center;
    order: 2;
  }

  .heroTitle {
    justify-content: center;
    margin-bottom: 1rem;
  }

  .vllmLogo {
    height: 60px;
  }

  .codeImage {
    max-width: 90%;
    max-height: 300px;
    margin: 0 auto;
  }

  .buttons {
    margin-top: 1.5rem;
    flex-direction: column;
    gap: 1rem;
  }

  .techBadges {
    justify-content: center;
  }

  .aiTechContainer {
    flex-direction: column;
    gap: 2rem;
    padding: 0 1rem;
  }

  .aiTechTitle {
    font-size: 2rem;
    text-align: center;
  }

  .aiFeatures {
    grid-template-columns: 1fr;
  }
}




