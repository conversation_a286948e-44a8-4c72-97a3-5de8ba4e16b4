/* Tech-inspired background with animated grid */
@keyframes gridMove {
  0% { transform: translate(0, 0); }
  100% { transform: translate(20px, 20px); }
}

@keyframes glow {
  0%, 100% { box-shadow: 0 0 5px rgba(0, 123, 255, 0.3); }
  50% { box-shadow: 0 0 20px rgba(0, 123, 255, 0.6), 0 0 30px rgba(0, 123, 255, 0.4); }
}

@keyframes pulse {
  0%, 100% { opacity: 0.8; }
  50% { opacity: 1; }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.subtitle {
  font-size: 1.3rem;
  background: linear-gradient(135deg, #007bff, #00d4ff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 2rem;
  font-weight: 600;
  text-align: center;
  animation: slideIn 0.8s ease-out;
}

.goalSection {
  background: linear-gradient(135deg, rgba(0, 123, 255, 0.08), rgba(0, 212, 255, 0.08));
  border: 1px solid rgba(0, 123, 255, 0.25);
  padding: 2rem;
  border-radius: 12px;
  margin-bottom: 2rem;
  position: relative;
  overflow: hidden;
  animation: slideIn 0.8s ease-out 0.2s both;
  color: var(--ifm-color-emphasis-800);
}

.goalSection::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    linear-gradient(rgba(0, 123, 255, 0.06) 1px, transparent 1px),
    linear-gradient(90deg, rgba(0, 123, 255, 0.06) 1px, transparent 1px);
  background-size: 20px 20px;
  animation: gridMove 20s linear infinite;
  pointer-events: none;
}

.goalSection h2,
.goalSection h3 {
  color: var(--ifm-color-emphasis-900);
}

.keyDeliverables {
  margin-top: 1.5rem;
  padding-top: 1.5rem;
  border-top: 2px solid transparent;
  background: linear-gradient(90deg, rgba(0, 123, 255, 0.4), rgba(0, 212, 255, 0.4)) top/100% 2px no-repeat;
}

.priorityLegend {
  background: linear-gradient(135deg, rgba(0, 123, 255, 0.08), rgba(0, 212, 255, 0.08));
  border: 1px solid rgba(0, 123, 255, 0.3);
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 2rem;
  backdrop-filter: blur(10px);
  position: relative;
  animation: slideIn 0.8s ease-out 0.4s both;
  color: var(--ifm-color-emphasis-800);
}

.priorityLegend::before {
  content: '';
  position: absolute;
  top: -1px;
  left: -1px;
  right: -1px;
  bottom: -1px;
  background: linear-gradient(45deg, #007bff, #00d4ff, #007bff);
  border-radius: 12px;
  z-index: -1;
  animation: glow 3s ease-in-out infinite;
}

.priorityLegend h3 {
  color: var(--ifm-color-emphasis-900);
  margin-bottom: 1rem;
}

.priorityItems {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-top: 1rem;
}

.priorityItem {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 0.75rem;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(0, 123, 255, 0.15);
  transition: all 0.3s ease;
}

.priorityItem:hover {
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(0, 123, 255, 0.3);
  transform: translateX(5px);
}

.priorityItem strong {
  color: var(--ifm-color-emphasis-900);
}

.priorityItem p {
  margin: 0.25rem 0 0 0;
  color: var(--ifm-color-emphasis-700);
  font-size: 0.9rem;
}

.priorityBadge {
  display: inline-block;
  padding: 0.4rem 0.8rem;
  border-radius: 20px;
  color: white;
  font-weight: bold;
  font-size: 0.75rem;
  text-align: center;
  min-width: 2.5rem;
  flex-shrink: 0;
  position: relative;
  overflow: hidden;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  animation: pulse 2s ease-in-out infinite;
}

.priorityBadge::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.priorityBadge:hover::before {
  left: 100%;
}

.areaSection {
  margin-bottom: 3rem;
  border: 1px solid rgba(0, 123, 255, 0.25);
  border-radius: 12px;
  overflow: hidden;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.03), rgba(0, 123, 255, 0.03));
  backdrop-filter: blur(5px);
  position: relative;
  animation: slideIn 0.8s ease-out var(--delay, 0.6s) both;
}

.areaSection::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #007bff, #00d4ff, #007bff);
  animation: glow 3s ease-in-out infinite;
}

.areaTitle {
  background: linear-gradient(135deg, #007bff, #0056b3);
  color: white;
  margin: 0;
  padding: 1.2rem 1.5rem;
  font-size: 1.3rem;
  font-weight: 600;
  position: relative;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.areaTitle::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
}

.areaContent {
  padding: 2rem 1.5rem;
  background: rgba(255, 255, 255, 0.02);
  color: var(--ifm-color-emphasis-800);
}

.subsection {
  margin-bottom: 2.5rem;
  position: relative;
}

.subsection:last-child {
  margin-bottom: 0;
}

.subsection h4 {
  color: #007bff;
  border-bottom: none;
  padding: 0.75rem 1rem;
  margin-bottom: 1.5rem;
  background: linear-gradient(135deg, rgba(0, 123, 255, 0.1), rgba(0, 212, 255, 0.1));
  border-left: 4px solid #007bff;
  border-radius: 0 8px 8px 0;
  font-weight: 600;
  position: relative;
  overflow: hidden;
}

.subsection h4::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  width: 4px;
  background: linear-gradient(180deg, #007bff, #00d4ff);
  animation: pulse 2s ease-in-out infinite;
}

.roadmapItem {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.08), rgba(0, 123, 255, 0.04));
  border: 1px solid rgba(0, 123, 255, 0.2);
  border-radius: 10px;
  padding: 1.25rem;
  margin-bottom: 1.25rem;
  position: relative;
  transition: all 0.3s ease;
  backdrop-filter: blur(5px);
  color: var(--ifm-color-emphasis-800);
}

.roadmapItem::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 10px;
  background: linear-gradient(135deg, rgba(0, 123, 255, 0.08), rgba(0, 212, 255, 0.08));
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.roadmapItem:hover {
  transform: translateY(-2px);
  border-color: rgba(0, 123, 255, 0.4);
  box-shadow:
    0 8px 25px rgba(0, 123, 255, 0.15),
    0 0 0 1px rgba(0, 123, 255, 0.1);
}

.roadmapItem:hover::before {
  opacity: 1;
}

.itemHeader {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 0.75rem;
  gap: 1rem;
}

.itemTitle {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--ifm-color-emphasis-900);
  flex: 1;
  position: relative;
}

.itemTitle::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, #007bff, #00d4ff);
  transition: width 0.3s ease;
}

.roadmapItem:hover .itemTitle::after {
  width: 100%;
}

.taskLink {
  color: #007bff;
  text-decoration: none;
  font-weight: 700;
  font-size: 0.9rem;
  padding: 0.2rem 0.5rem;
  border-radius: 4px;
  background: rgba(0, 123, 255, 0.1);
  border: 1px solid rgba(0, 123, 255, 0.2);
  transition: all 0.3s ease;
  display: inline-block;
  margin-right: 0.5rem;
  position: relative;
  overflow: hidden;
}

.taskLink::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.3s ease;
}

.taskLink:hover {
  color: #0056b3;
  background: rgba(0, 123, 255, 0.15);
  border-color: rgba(0, 123, 255, 0.4);
  text-decoration: none;
  transform: scale(1.05);
}

.taskLink:hover::before {
  left: 100%;
}

.itemDescription {
  margin-bottom: 1.25rem;
  color: var(--ifm-color-emphasis-700);
  line-height: 1.6;
  font-size: 0.95rem;
}

.acceptance {
  background: linear-gradient(135deg, rgba(40, 167, 69, 0.08), rgba(25, 135, 84, 0.08));
  border: 1px solid rgba(40, 167, 69, 0.25);
  border-left: 4px solid #28a745;
  padding: 1rem;
  border-radius: 8px;
  font-size: 0.9rem;
  line-height: 1.5;
  position: relative;
  overflow: hidden;
  color: var(--ifm-color-emphasis-800);
}

.acceptance::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  width: 4px;
  background: linear-gradient(180deg, #28a745, #20c997);
  animation: pulse 3s ease-in-out infinite;
}

.acceptance strong {
  color: #1e7e34;
  font-weight: 700;
}

/* Tech-inspired scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 123, 255, 0.1);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, #007bff, #00d4ff);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, #0056b3, #007bff);
}

/* Staggered animation delays for area sections */
.areaSection:nth-child(1) { --delay: 0.6s; }
.areaSection:nth-child(2) { --delay: 0.8s; }
.areaSection:nth-child(3) { --delay: 1.0s; }
.areaSection:nth-child(4) { --delay: 1.2s; }
.areaSection:nth-child(5) { --delay: 1.4s; }
.areaSection:nth-child(6) { --delay: 1.6s; }
.areaSection:nth-child(7) { --delay: 1.8s; }
.areaSection:nth-child(8) { --delay: 2.0s; }

/* Responsive design */
@media (max-width: 768px) {
  .priorityItems {
    gap: 1.5rem;
  }

  .priorityItem {
    flex-direction: column;
    gap: 0.5rem;
  }

  .itemHeader {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .goalSection,
  .priorityLegend,
  .areaContent {
    padding: 1rem;
  }

  .subtitle {
    font-size: 1.1rem;
  }

  .areaTitle {
    font-size: 1.1rem;
    padding: 1rem;
  }

  .roadmapItem {
    padding: 1rem;
  }

  /* Reduce animation delays on mobile */
  .areaSection {
    --delay: 0.2s;
  }
}
