# Overview

Semantic Router provides observability features including metrics, logging, and health checks to monitor routing performance and system reliability.

## Core Concepts

### Prometheus Metrics

Exposes detailed metrics for routing performance, security events, and system health.

### Health Checks

Provides health endpoints for monitoring service and dependency status.

### Structured Logging

Comprehensive logging for request tracing, security events, and performance analysis.
## Key Features

- **Prometheus Integration**: Exposes detailed metrics on port 9190
- **Health Endpoints**: Service and dependency health monitoring
- **Pre-built Dashboards**: Grafana dashboards for common monitoring needs
- **Structured Logging**: JSON-formatted logs for easy analysis
